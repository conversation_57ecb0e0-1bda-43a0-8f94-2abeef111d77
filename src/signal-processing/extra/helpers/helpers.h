#if !defined(_PROCESSING_HELPERS_INCLUDED_)
#define _PROCESSING_HELPERS_INCLUDED_

#include "../../universal-value.h"
#include "../../gdefs.h"
#include "../processing-buffer.h"
#include "../../universal-source/universal-source.h"
#include "../processing-last-error.h"

#define max(x, y) ((x) > (y) ? (x) : (y))
#define min(x, y) ((x) < (y) ? (x) : (y))

#define MAX_THRESHOLD_SIZE          static_cast<uSmSize>(PROCESSING_BUFF_SIZE / sizeof(uValueLarge))
#define MAX_AVE_SIZE                static_cast<uSmSize>(MAX_READ_SIZE - 3)

namespace Processing
{
namespace Helpers
{
    inline bool validateIndex(double index, CUniversalSource& source)
    {
        uPos maxIndex = source.samples() - 1;
        if (index < 0 || index > maxIndex)
        {
            Throw::indexOutsideBoundaries(index, 0, maxIndex);
            return false;
        }
        return true;
    }
    
    inline bool validatePosition(double position, CUniversalSource& source)
    {
        uPos startPosition = source.getStartPos();
        uPos endPosition = source.getEndPos();
        if (position < startPosition || position > endPosition)
        {
            Throw::positionOutsideBoundaries(position, startPosition, endPosition);
            return false;
        }
        return true;
    }

    inline bool getAveSizes(double dblAveSize, uSmSize& aveSize, uSmSize& halfAveSize)
    {
        if (dblAveSize < 0) {
            Throw::averageNegative();
            return false;
        }
        dblAveSize = dblAveSize >= 1 ? dblAveSize : 1;
        aveSize = static_cast<unsigned int>(dblAveSize + 0.5);
        halfAveSize = aveSize >> 1;
        aveSize = (halfAveSize << 1) + 1;

        if (aveSize > MAX_AVE_SIZE) {
            Throw::averageOutsideBoundaries(aveSize, MAX_AVE_SIZE);
            return false;
        }
        return true;
    }

    inline bool getThresholdSize(double dblThreshold, uSmSize& thresholdSize)
    {
        if (dblThreshold < 0) {
            Throw::thresholdNegative();
            return false;
        }
        dblThreshold = dblThreshold >= 1 ? dblThreshold : 1;
        thresholdSize = static_cast<unsigned int>(dblThreshold + 0.5);

        if (thresholdSize > MAX_THRESHOLD_SIZE) {
            Throw::thresholdOutsideBoundaries(thresholdSize, MAX_THRESHOLD_SIZE);
            return false;
        }
        return true;
    }

    inline double getSearchedValuePos(uValueLarge* pProcessingBuff, uValueLarge searchedValue, uValueLarge currentValue, uValueLarge previousValue, uPos thresholdEndPos, uSmPos thresholdEndIndex, uSmSize thresholdSize, bool isRisen)
    {
        uSmSize i = 1;
        if (thresholdSize > 1)
        {
            while (i < thresholdSize)
            {
                currentValue = pProcessingBuff[thresholdEndIndex--];
                if (thresholdEndIndex < 0)
                    thresholdEndIndex = thresholdSize - 1;
                previousValue = pProcessingBuff[thresholdEndIndex];

                if (isRisen)
                {
                    if (previousValue <= searchedValue && searchedValue <= currentValue)
                    {
                        break;
                    }
                }
                else
                {
                    if (previousValue >= searchedValue && searchedValue >= currentValue)
                    {
                        break;
                    }
                }
                
                i++;
            }
        }

        double searchPosOffset = static_cast<double>(searchedValue - previousValue) / static_cast<double>(currentValue - previousValue);
        return static_cast<double>(thresholdEndPos - i) + searchPosOffset;
    }

    inline void adaptFrwdProcessingFragment(bool toEOF, uSmSize halfAveSize, uPos sourceMaxPos, uPos& processingStartPos, uSize& elements, double& dblEndPosition)
    {
        uPos endPos;
        elements = elements > 0 ? elements : 0;
        
        if (toEOF) 
        {
            processingStartPos = max(halfAveSize, processingStartPos);
            endPos = sourceMaxPos - halfAveSize;
            dblEndPosition = static_cast<double>(sourceMaxPos);
        }
        else 
        {
            if (halfAveSize > processingStartPos)
            {
                elements -= halfAveSize - processingStartPos;
                processingStartPos = halfAveSize;
            }
            endPos = processingStartPos + elements;
            endPos = min(endPos, sourceMaxPos - halfAveSize);
        }
        
        elements = endPos - processingStartPos + 1;
    }

    inline void prepareFrwdThresholdAveData(CUniversalSource& source, uPos startPos, uSmSize thresholdSize, uSmSize aveSize, uSmSize halfAveSize)
    {
        uValue const* pData;
        uSmSize unprocessed = thresholdSize, processed, i;
        uValueLarge* pProcessingBuff = reinterpret_cast<uValueLarge *>(ProcessingBuffer);

        if (aveSize > 1)
        {
            uValueLarge summ = 0;
            bool firstCycle = true;
            uSmPos aveFirst, aveLast;
            startPos -= halfAveSize;

            while (unprocessed > 0)
            {
                uSmSize portion;
                if (firstCycle)
                {
                    firstCycle = false;
                    portion = min(unprocessed + aveSize - 1, MAX_READ_SIZE);
                    pData = source.getData(startPos--, portion);

                    for (i = 0; i < aveSize; i++)
                        summ += pData[i];

                    processed = i = 1;
                    pProcessingBuff[0] = summ;
                }
                else
                {
                    portion = min(unprocessed + aveSize, MAX_READ_SIZE);
                    pData = source.getData(startPos, portion);
                    processed = 0;
                }

                aveFirst = 0, aveLast = aveSize;

                while (aveLast < portion)
                {
                    summ -= pData[aveFirst++];
                    summ += pData[aveLast++];
                    pProcessingBuff[i++] = summ;
                }

                processed += portion - aveSize;
                startPos += processed;
                unprocessed -= processed;
            }
        }
        else
        {
            i = 0;
            while (unprocessed > 0)
            {
                uSmSize portion = min(unprocessed, MAX_READ_SIZE);
                pData = source.getData(startPos, portion);

                processed = 0;
                while (processed < portion)
                {
                    pProcessingBuff[i++] = pData[processed++];
                }

                startPos += processed;
                unprocessed -= processed;
            }
        }
    }
}
}

#endif
